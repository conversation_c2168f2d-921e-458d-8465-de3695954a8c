{"version": 3, "sources": ["../../../../src/client/components/router-reducer/invalidate-cache-by-router-state.ts"], "sourcesContent": ["import type { CacheNode } from '../../../shared/lib/app-router-context.shared-runtime'\nimport type { FlightRouterState } from '../../../server/app-render/types'\nimport { createRouterCacheKey } from './create-router-cache-key'\n\n/**\n * Invalidate cache one level down from the router state.\n */\nexport function invalidateCacheByRouterState(\n  newCache: CacheNode,\n  existingCache: CacheNode,\n  routerState: FlightRouterState\n): void {\n  // Remove segment that we got data for so that it is filled in during rendering of rsc.\n  for (const key in routerState[1]) {\n    const segmentForParallelRoute = routerState[1][key][0]\n    const cacheKey = createRouterCacheKey(segmentForParallelRoute)\n    const existingParallelRoutesCacheNode =\n      existingCache.parallelRoutes.get(key)\n    if (existingParallelRoutesCacheNode) {\n      let parallelRouteCacheNode = new Map(existingParallelRoutesCacheNode)\n      parallelRouteCacheNode.delete(cacheKey)\n      newCache.parallelRoutes.set(key, parallelRouteCacheNode)\n    }\n  }\n}\n"], "names": ["createRouterCache<PERSON>ey", "invalidateCacheByRouterState", "newCache", "existingCache", "routerState", "key", "segmentForParallelRoute", "cache<PERSON>ey", "existingParallelRoutesCacheNode", "parallelRoutes", "get", "parallelRouteCacheNode", "Map", "delete", "set"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,4BAA2B;AAEhE;;CAEC,GACD,OAAO,SAASC,6BACdC,QAAmB,EACnBC,aAAwB,EACxBC,WAA8B;IAE9B,uFAAuF;IACvF,IAAK,MAAMC,OAAOD,WAAW,CAAC,EAAE,CAAE;QAChC,MAAME,0BAA0BF,WAAW,CAAC,EAAE,CAACC,IAAI,CAAC,EAAE;QACtD,MAAME,WAAWP,qBAAqBM;QACtC,MAAME,kCACJL,cAAcM,cAAc,CAACC,GAAG,CAACL;QACnC,IAAIG,iCAAiC;YACnC,IAAIG,yBAAyB,IAAIC,IAAIJ;YACrCG,uBAAuBE,MAAM,CAACN;YAC9BL,SAASO,cAAc,CAACK,GAAG,CAACT,KAAKM;QACnC;IACF;AACF", "ignoreList": [0]}