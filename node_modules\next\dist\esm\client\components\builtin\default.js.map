{"version": 3, "sources": ["../../../../src/client/components/builtin/default.tsx"], "sourcesContent": ["import { notFound } from '../not-found'\n\nexport const PARALLEL_ROUTE_DEFAULT_PATH =\n  'next/dist/client/components/builtin/default.js'\n\nexport default function ParallelRouteDefault() {\n  notFound()\n}\n"], "names": ["notFound", "PARALLEL_ROUTE_DEFAULT_PATH", "<PERSON>llel<PERSON><PERSON><PERSON>ault"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,eAAc;AAEvC,OAAO,MAAMC,8BACX,iDAAgD;AAElD,eAAe,SAASC;IACtBF;AACF", "ignoreList": [0]}