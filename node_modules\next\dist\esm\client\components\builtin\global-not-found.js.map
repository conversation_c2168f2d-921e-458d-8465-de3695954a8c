{"version": 3, "sources": ["../../../../src/client/components/builtin/global-not-found.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from '../http-access-fallback/error-fallback'\n\nfunction GlobalNotFound() {\n  return (\n    <html>\n      <body>\n        <HTTPAccessErrorFallback\n          status={404}\n          message={'This page could not be found.'}\n        />\n      </body>\n    </html>\n  )\n}\n\nexport default GlobalNotFound\n"], "names": ["HTTPAccessErrorFallback", "GlobalNotFound", "html", "body", "status", "message"], "mappings": ";AAAA,SAASA,uBAAuB,QAAQ,yCAAwC;AAEhF,SAASC;IACP,qBACE,KAACC;kBACC,cAAA,KAACC;sBACC,cAAA,KAACH;gBACCI,QAAQ;gBACRC,SAAS;;;;AAKnB;AAEA,eAAeJ,eAAc", "ignoreList": [0]}